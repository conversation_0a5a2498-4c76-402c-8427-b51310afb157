@page
@model LibraryManagementSystem.UI.Pages.Users.IndexModel
@{
    ViewData["Title"] = "المستخدمون - Users";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-users"></i>
                        إدارة المستخدمين - Users Management
                    </h3>
                    <a href="/Users/<USER>" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إضافة مستخدم جديد - Add New User
                    </a>
                </div>
                <div class="card-body">
                    @if (Model.Users?.Any() == true)
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الرقم - ID</th>
                                        <th>الاسم الكامل - Full Name</th>
                                        <th>البريد الإلكتروني - Email</th>
                                        <th>رقم الهاتف - Phone</th>
                                        <th>العنوان - Address</th>
                                        <th>تاريخ العضوية - Membership Date</th>
                                        <th>الحالة - Status</th>
                                        <th>الإجراءات - Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var user in Model.Users)
                                    {
                                        <tr>
                                            <td>@user.UserId</td>
                                            <td>@user.FirstName @user.LastName</td>
                                            <td>@user.Email</td>
                                            <td>@user.PhoneNumber</td>
                                            <td>@user.Address</td>
                                            <td>@user.MembershipDate.ToString("yyyy-MM-dd")</td>
                                            <td>
                                                @if (user.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط - Active</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">غير نشط - Inactive</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="/Users/<USER>/@user.UserId" class="btn btn-info btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                        عرض - View
                                                    </a>
                                                    <a href="/Users/<USER>/@user.UserId" class="btn btn-warning btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                        تعديل - Edit
                                                    </a>
                                                    <button type="button" class="btn btn-danger btn-sm" onclick="confirmDelete(@user.UserId)">
                                                        <i class="fas fa-trash"></i>
                                                        حذف - Delete
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i>
                            لا توجد مستخدمون مسجلون حالياً - No users registered yet
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟ - Are you sure you want to delete this user?')) {
        // إرسال طلب حذف
        fetch(`/Users/<USER>/${userId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('فشل في حذف المستخدم - Failed to delete user');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الحذف - An error occurred during deletion');
        });
    }
}
</script>
