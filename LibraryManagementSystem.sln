﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36221.1
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LibraryManagementSystem.DAL", "LibraryManagementSystem.DAL\LibraryManagementSystem.DAL.csproj", "{36B2881E-CB42-403B-964A-D4D78DC97E81}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LibraryManagementSystem.BLL", "LibraryManagementSystem.BLL\LibraryManagementSystem.BLL.csproj", "{B8940DE7-E288-4788-BBF3-98183379C28E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LibraryManagementSystem.UI", "LibraryManagementSystem.UI\LibraryManagementSystem.UI.csproj", "{45C599BA-678A-4C5D-B09E-F25BD0DAD4B1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LibraryManagementSystem.Tests", "LibraryManagementSystem.Tests\LibraryManagementSystem.Tests.csproj", "{E10B85E6-0770-43E2-8C7E-9A6C9390491F}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{36B2881E-CB42-403B-964A-D4D78DC97E81}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{36B2881E-CB42-403B-964A-D4D78DC97E81}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{36B2881E-CB42-403B-964A-D4D78DC97E81}.Debug|x64.ActiveCfg = Debug|Any CPU
		{36B2881E-CB42-403B-964A-D4D78DC97E81}.Debug|x64.Build.0 = Debug|Any CPU
		{36B2881E-CB42-403B-964A-D4D78DC97E81}.Debug|x86.ActiveCfg = Debug|Any CPU
		{36B2881E-CB42-403B-964A-D4D78DC97E81}.Debug|x86.Build.0 = Debug|Any CPU
		{36B2881E-CB42-403B-964A-D4D78DC97E81}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{36B2881E-CB42-403B-964A-D4D78DC97E81}.Release|Any CPU.Build.0 = Release|Any CPU
		{36B2881E-CB42-403B-964A-D4D78DC97E81}.Release|x64.ActiveCfg = Release|Any CPU
		{36B2881E-CB42-403B-964A-D4D78DC97E81}.Release|x64.Build.0 = Release|Any CPU
		{36B2881E-CB42-403B-964A-D4D78DC97E81}.Release|x86.ActiveCfg = Release|Any CPU
		{36B2881E-CB42-403B-964A-D4D78DC97E81}.Release|x86.Build.0 = Release|Any CPU
		{B8940DE7-E288-4788-BBF3-98183379C28E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B8940DE7-E288-4788-BBF3-98183379C28E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B8940DE7-E288-4788-BBF3-98183379C28E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B8940DE7-E288-4788-BBF3-98183379C28E}.Debug|x64.Build.0 = Debug|Any CPU
		{B8940DE7-E288-4788-BBF3-98183379C28E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B8940DE7-E288-4788-BBF3-98183379C28E}.Debug|x86.Build.0 = Debug|Any CPU
		{B8940DE7-E288-4788-BBF3-98183379C28E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B8940DE7-E288-4788-BBF3-98183379C28E}.Release|Any CPU.Build.0 = Release|Any CPU
		{B8940DE7-E288-4788-BBF3-98183379C28E}.Release|x64.ActiveCfg = Release|Any CPU
		{B8940DE7-E288-4788-BBF3-98183379C28E}.Release|x64.Build.0 = Release|Any CPU
		{B8940DE7-E288-4788-BBF3-98183379C28E}.Release|x86.ActiveCfg = Release|Any CPU
		{B8940DE7-E288-4788-BBF3-98183379C28E}.Release|x86.Build.0 = Release|Any CPU
		{45C599BA-678A-4C5D-B09E-F25BD0DAD4B1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{45C599BA-678A-4C5D-B09E-F25BD0DAD4B1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{45C599BA-678A-4C5D-B09E-F25BD0DAD4B1}.Debug|x64.ActiveCfg = Debug|Any CPU
		{45C599BA-678A-4C5D-B09E-F25BD0DAD4B1}.Debug|x64.Build.0 = Debug|Any CPU
		{45C599BA-678A-4C5D-B09E-F25BD0DAD4B1}.Debug|x86.ActiveCfg = Debug|Any CPU
		{45C599BA-678A-4C5D-B09E-F25BD0DAD4B1}.Debug|x86.Build.0 = Debug|Any CPU
		{45C599BA-678A-4C5D-B09E-F25BD0DAD4B1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{45C599BA-678A-4C5D-B09E-F25BD0DAD4B1}.Release|Any CPU.Build.0 = Release|Any CPU
		{45C599BA-678A-4C5D-B09E-F25BD0DAD4B1}.Release|x64.ActiveCfg = Release|Any CPU
		{45C599BA-678A-4C5D-B09E-F25BD0DAD4B1}.Release|x64.Build.0 = Release|Any CPU
		{45C599BA-678A-4C5D-B09E-F25BD0DAD4B1}.Release|x86.ActiveCfg = Release|Any CPU
		{45C599BA-678A-4C5D-B09E-F25BD0DAD4B1}.Release|x86.Build.0 = Release|Any CPU
		{E10B85E6-0770-43E2-8C7E-9A6C9390491F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E10B85E6-0770-43E2-8C7E-9A6C9390491F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E10B85E6-0770-43E2-8C7E-9A6C9390491F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{E10B85E6-0770-43E2-8C7E-9A6C9390491F}.Debug|x64.Build.0 = Debug|Any CPU
		{E10B85E6-0770-43E2-8C7E-9A6C9390491F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E10B85E6-0770-43E2-8C7E-9A6C9390491F}.Debug|x86.Build.0 = Debug|Any CPU
		{E10B85E6-0770-43E2-8C7E-9A6C9390491F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E10B85E6-0770-43E2-8C7E-9A6C9390491F}.Release|Any CPU.Build.0 = Release|Any CPU
		{E10B85E6-0770-43E2-8C7E-9A6C9390491F}.Release|x64.ActiveCfg = Release|Any CPU
		{E10B85E6-0770-43E2-8C7E-9A6C9390491F}.Release|x64.Build.0 = Release|Any CPU
		{E10B85E6-0770-43E2-8C7E-9A6C9390491F}.Release|x86.ActiveCfg = Release|Any CPU
		{E10B85E6-0770-43E2-8C7E-9A6C9390491F}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A4C9B654-66AD-46E2-B28F-4652B553E8ED}
	EndGlobalSection
EndGlobal
