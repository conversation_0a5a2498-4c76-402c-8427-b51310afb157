is_global = true
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = LibraryManagementSystem.UI
build_property.RootNamespace = LibraryManagementSystem.UI
build_property.ProjectDir = C:\Titan Task\LibraryManagementSystem\LibraryManagementSystem.UI\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = true
build_property.MSBuildProjectDirectory = C:\Titan Task\LibraryManagementSystem\LibraryManagementSystem.UI
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[C:/Titan Task/LibraryManagementSystem/LibraryManagementSystem.UI/Pages/Books/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQm9va3NcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Titan Task/LibraryManagementSystem/LibraryManagementSystem.UI/Pages/Borrowings/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQm9ycm93aW5nc1xJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Titan Task/LibraryManagementSystem/LibraryManagementSystem.UI/Pages/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Titan Task/LibraryManagementSystem/LibraryManagementSystem.UI/Pages/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Titan Task/LibraryManagementSystem/LibraryManagementSystem.UI/Pages/Statistics/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU3RhdGlzdGljc1xJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Titan Task/LibraryManagementSystem/LibraryManagementSystem.UI/Pages/Users/<USER>
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcVXNlcnNcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Titan Task/LibraryManagementSystem/LibraryManagementSystem.UI/Pages/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Titan Task/LibraryManagementSystem/LibraryManagementSystem.UI/Pages/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 
