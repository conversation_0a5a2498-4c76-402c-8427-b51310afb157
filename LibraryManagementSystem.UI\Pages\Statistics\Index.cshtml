@page
@model LibraryManagementSystem.UI.Pages.Statistics.IndexModel
@{
    ViewData["Title"] = "الإحصائيات - Statistics";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">
                <i class="fas fa-chart-bar"></i>
                إحصائيات المكتبة - Library Statistics
            </h2>
        </div>
    </div>

    <!-- إحصائيات عامة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@Model.BookStatistics?.TotalBooks</h4>
                            <p class="mb-0">إجمالي الكتب - Total Books</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-book fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@Model.BookStatistics?.AvailableBooks</h4>
                            <p class="mb-0">الكتب المتاحة - Available Books</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@Model.BorrowingStatistics?.ActiveBorrowings</h4>
                            <p class="mb-0">الاستعارات النشطة - Active Borrowings</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-hand-holding fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@Model.UserCount</h4>
                            <p class="mb-0">إجمالي المستخدمين - Total Users</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- الكتب الأكثر استعارة -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-trophy"></i>
                        الكتب الأكثر استعارة - Most Borrowed Books
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.MostBorrowedBooks?.Any() == true)
                    {
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>الترتيب - Rank</th>
                                        <th>عنوان الكتاب - Book Title</th>
                                        <th>المؤلف - Author</th>
                                        <th>عدد الاستعارات - Borrow Count</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @{
                                        int rank = 1;
                                    }
                                    @foreach (var book in Model.MostBorrowedBooks.Take(10))
                                    {
                                        <tr>
                                            <td>
                                                @if (rank == 1)
                                                {
                                                    <i class="fas fa-trophy text-warning"></i>
                                                }
                                                else if (rank == 2)
                                                {
                                                    <i class="fas fa-medal text-secondary"></i>
                                                }
                                                else if (rank == 3)
                                                {
                                                    <i class="fas fa-award text-warning"></i>
                                                }
                                                else
                                                {
                                                    <span>@rank</span>
                                                }
                                            </td>
                                            <td>@book.Title</td>
                                            <td>@book.Author</td>
                                            <td>
                                                <span class="badge bg-primary">@book.BorrowCount</span>
                                            </td>
                                        </tr>
                                        rank++;
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            لا توجد بيانات استعارة متاحة - No borrowing data available
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- المستخدمون الأكثر نشاطاً -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-star"></i>
                        المستخدمون الأكثر نشاطاً - Most Active Users
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.MostActiveUsers?.Any() == true)
                    {
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>الترتيب - Rank</th>
                                        <th>اسم المستخدم - User Name</th>
                                        <th>البريد الإلكتروني - Email</th>
                                        <th>عدد الاستعارات - Borrow Count</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @{
                                        int userRank = 1;
                                    }
                                    @foreach (var user in Model.MostActiveUsers.Take(10))
                                    {
                                        <tr>
                                            <td>
                                                @if (userRank == 1)
                                                {
                                                    <i class="fas fa-crown text-warning"></i>
                                                }
                                                else if (userRank == 2)
                                                {
                                                    <i class="fas fa-medal text-secondary"></i>
                                                }
                                                else if (userRank == 3)
                                                {
                                                    <i class="fas fa-award text-warning"></i>
                                                }
                                                else
                                                {
                                                    <span>@userRank</span>
                                                }
                                            </td>
                                            <td>@user.FullName</td>
                                            <td>@user.Email</td>
                                            <td>
                                                <span class="badge bg-success">@user.TotalBorrowings</span>
                                            </td>
                                        </tr>
                                        userRank++;
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            لا توجد بيانات مستخدمين متاحة - No user data available
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات إضافية -->
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title">إحصائيات الكتب - Book Statistics</h6>
                </div>
                <div class="card-body">
                    @if (Model.BookStatistics != null)
                    {
                        <ul class="list-unstyled">
                            <li><strong>إجمالي النسخ:</strong> @Model.BookStatistics.TotalCopies</li>
                            <li><strong>النسخ المتاحة:</strong> @Model.BookStatistics.AvailableCopies</li>
                            <li><strong>النسخ المستعارة:</strong> @(Model.BookStatistics.TotalCopies -
                                                            Model.BookStatistics.AvailableCopies)</li>
                            <li><strong>معدل الاستخدام:</strong> @(Model.BookStatistics.TotalCopies > 0 ?
                                                            Math.Round((double)(Model.BookStatistics.TotalCopies - Model.BookStatistics.AvailableCopies)
                                                            / Model.BookStatistics.TotalCopies * 100, 1) : 0)%</li>
                        </ul>
                    }
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title">إحصائيات الاستعارات - Borrowing Statistics</h6>
                </div>
                <div class="card-body">
                    @if (Model.BorrowingStatistics != null)
                    {
                        <ul class="list-unstyled">
                            <li><strong>إجمالي الاستعارات:</strong> @Model.BorrowingStatistics.TotalBorrowings</li>
                            <li><strong>الاستعارات النشطة:</strong> @Model.BorrowingStatistics.ActiveBorrowings</li>
                            <li><strong>الاستعارات المرجعة:</strong> @Model.BorrowingStatistics.ReturnedBorrowings</li>
                            <li><strong>الاستعارات المتأخرة:</strong> @Model.BorrowingStatistics.OverdueBorrowings</li>
                        </ul>
                    }
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title">معلومات النظام - System Info</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><strong>تاريخ آخر تحديث:</strong> @DateTime.Now.ToString("yyyy-MM-dd HH:mm")</li>
                        <li><strong>حالة النظام:</strong> <span class="badge bg-success">يعمل - Running</span></li>
                        <li><strong>قاعدة البيانات:</strong> <span class="badge bg-info">متصلة - Connected</span></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
