{"Version": 1, "Hash": "MY4R4xn5zol/uKGb2jxUfy10zl7k4oz0X0fc43XrXoA=", "Source": "LibraryManagementSystem.UI", "BasePath": "_content/LibraryManagementSystem.UI", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "LibraryManagementSystem.UI\\wwwroot", "Source": "LibraryManagementSystem.UI", "ContentRoot": "C:\\Titan Task\\LibraryManagementSystem\\LibraryManagementSystem.UI\\wwwroot\\", "BasePath": "_content/LibraryManagementSystem.UI", "Pattern": "**"}], "Assets": [], "Endpoints": []}