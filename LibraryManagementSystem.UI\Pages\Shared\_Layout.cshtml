<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - نظام إدارة المكتبة</title>

    <!-- Bootstrap CSS with RTL support -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        /* تخصيص الخطوط العربية - Arabic font customization */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }

        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0a58ca;
        }

        .table th {
            background-color: #e9ecef;
            font-weight: 600;
        }

        .search-container {
            background: white;
            border-radius: 0.375rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }

        .footer {
            background-color: #343a40;
            color: white;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        /* تحسينات للعناصر التفاعلية - Interactive elements improvements */
        .btn {
            border-radius: 0.375rem;
            font-weight: 500;
        }

        .form-control:focus {
            border-color: #86b7fe;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        /* تحسينات للجداول - Table improvements */
        .table-hover tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.075);
        }

        /* تحسينات للتنبيهات - Alert improvements */
        .alert {
            border-radius: 0.375rem;
            border: none;
        }

        /* تحسينات للشارات - Badge improvements */
        .badge {
            font-size: 0.75em;
            font-weight: 500;
        }
    </style>
</head>

<body>
    <header>
        <!-- شريط التنقل الرئيسي - Main navigation bar -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-book-open me-2"></i>
                    نظام إدارة المكتبة
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="/Books">
                                <i class="fas fa-book me-1"></i>
                                الكتب المتاحة
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/Borrowings">
                                <i class="fas fa-exchange-alt me-1"></i>
                                الاستعارات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/Users">
                                <i class="fas fa-users me-1"></i>
                                المستخدمون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/Statistics">
                                <i class="fas fa-chart-bar me-1"></i>
                                الإحصائيات
                            </a>
                        </li>
                    </ul>

                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                الحساب
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/Profile">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="/Settings">الإعدادات</a></li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li><a class="dropdown-item" href="/Logout">تسجيل الخروج</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main class="container mt-4">
        <!-- عرض الرسائل - Display messages -->
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                @TempData["SuccessMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        }

        @if (TempData["ErrorMessage"] != null)
        {
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                @TempData["ErrorMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        }

        @if (TempData["InfoMessage"] != null)
        {
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                @TempData["InfoMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        }

        <!-- محتوى الصفحة الرئيسي - Main page content -->
        @RenderBody()
    </main>

    <!-- تذييل الصفحة - Page footer -->
    <footer class="footer mt-auto">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>نظام إدارة المكتبة</h5>
                    <p class="mb-0">نظام شامل لإدارة المكتبات والاستعارات</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        <i class="fas fa-envelope me-2"></i>
                        <EMAIL>
                    </p>
                    <p class="mb-0">
                        <i class="fas fa-phone me-2"></i>
                        +966-11-1234567
                    </p>
                </div>
            </div>
            <hr class="my-3">
            <div class="row">
                <div class="col-12 text-center">
                    <p class="mb-0">&copy; @DateTime.Now.Year جميع الحقوق محفوظة - نظام إدارة المكتبة</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- تطبيق JavaScript مخصص - Custom JavaScript -->
    <script>
        // إخفاء التنبيهات تلقائياً بعد 5 ثوان
        // Auto-hide alerts after 5 seconds
        setTimeout(function () {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function (alert) {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // تحسين تجربة المستخدم للنماذج
        // Improve user experience for forms
        document.addEventListener('DOMContentLoaded', function () {
            // إضافة تأثيرات التحميل للأزرار
            // Add loading effects to buttons
            var submitButtons = document.querySelectorAll('button[type="submit"]');
            submitButtons.forEach(function (button) {
                button.addEventListener('click', function () {
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
                    this.disabled = true;
                });
            });
        });
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>

</html>
